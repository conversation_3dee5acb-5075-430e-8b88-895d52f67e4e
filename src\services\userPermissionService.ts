import { Permission, Role } from '@prisma/client';
import { DatabaseService } from './database.js';

export interface UserPermissionInfo {
  userid: string;
  permissions: Permission[];
  roles: Role[];
  directRoles: Role[]; // 直接分配的角色
  inheritedRoles: Role[]; // 从部门继承的角色
}

export interface AssignUserRoleData {
  userid: string;
  roleIds: string[];
  assignedBy: string;
  expiresAt?: Date;
}

export interface AssignDepartmentRoleData {
  deptId: number;
  roleIds: string[];
  assignedBy: string;
  expiresAt?: Date;
}

export class UserPermissionService {
  constructor(private databaseService: DatabaseService) {}

  /**
   * 获取用户的所有权限（包括直接分配和部门继承）
   */
  async getUserPermissions(userid: string): Promise<Permission[]> {
    try {
      // 获取用户信息
      const user = await this.databaseService.client.user.findUnique({
        where: { userid },
      });

      if (!user) {
        throw new Error(`用户不存在: ${userid}`);
      }

      const permissions = new Map<string, Permission>();

      // 1. 获取用户直接分配的角色权限
      const userRoles = await this.databaseService.client.userRole.findMany({
        where: {
          userid,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } },
          ],
        },
        include: {
          role: {
            include: {
              rolePermissions: {
                include: {
                  permission: true,
                },
              },
            },
          },
        },
      });

      // 添加直接角色的权限
      userRoles.forEach(userRole => {
        if (userRole.role.isActive) {
          userRole.role.rolePermissions.forEach(rp => {
            permissions.set(rp.permission.id, rp.permission);
          });
        }
      });

      // 2. 获取用户部门继承的角色权限
      if (user.deptIdList && user.deptIdList.length > 0) {
        const departmentRoles = await this.databaseService.client.departmentRole.findMany({
          where: {
            deptId: { in: user.deptIdList },
            OR: [
              { expiresAt: null },
              { expiresAt: { gt: new Date() } },
            ],
          },
          include: {
            role: {
              include: {
                rolePermissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        });

        // 添加部门角色的权限
        departmentRoles.forEach(deptRole => {
          if (deptRole.role.isActive) {
            deptRole.role.rolePermissions.forEach(rp => {
              permissions.set(rp.permission.id, rp.permission);
            });
          }
        });
      }

      return Array.from(permissions.values());
    } catch (error) {
      console.error('获取用户权限失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户的完整权限信息
   */
  async getUserPermissionInfo(userid: string): Promise<UserPermissionInfo> {
    try {
      // 获取用户信息
      const user = await this.databaseService.client.user.findUnique({
        where: { userid },
      });

      if (!user) {
        throw new Error(`用户不存在: ${userid}`);
      }

      const allPermissions = new Map<string, Permission>();
      const allRoles = new Map<string, Role>();
      const directRoles: Role[] = [];
      const inheritedRoles: Role[] = [];

      // 1. 获取用户直接分配的角色
      const userRoles = await this.databaseService.client.userRole.findMany({
        where: {
          userid,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } },
          ],
        },
        include: {
          role: {
            include: {
              rolePermissions: {
                include: {
                  permission: true,
                },
              },
            },
          },
        },
      });

      userRoles.forEach(userRole => {
        if (userRole.role.isActive) {
          directRoles.push(userRole.role);
          allRoles.set(userRole.role.id, userRole.role);
          
          userRole.role.rolePermissions.forEach(rp => {
            allPermissions.set(rp.permission.id, rp.permission);
          });
        }
      });

      // 2. 获取用户部门继承的角色
      if (user.deptIdList && user.deptIdList.length > 0) {
        const departmentRoles = await this.databaseService.client.departmentRole.findMany({
          where: {
            deptId: { in: user.deptIdList },
            OR: [
              { expiresAt: null },
              { expiresAt: { gt: new Date() } },
            ],
          },
          include: {
            role: {
              include: {
                rolePermissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        });

        departmentRoles.forEach(deptRole => {
          if (deptRole.role.isActive) {
            inheritedRoles.push(deptRole.role);
            allRoles.set(deptRole.role.id, deptRole.role);
            
            deptRole.role.rolePermissions.forEach(rp => {
              allPermissions.set(rp.permission.id, rp.permission);
            });
          }
        });
      }

      return {
        userid,
        permissions: Array.from(allPermissions.values()),
        roles: Array.from(allRoles.values()),
        directRoles,
        inheritedRoles,
      };
    } catch (error) {
      console.error('获取用户权限信息失败:', error);
      throw error;
    }
  }

  /**
   * 检查用户是否有指定权限
   */
  async hasPermission(userid: string, permissionName: string): Promise<boolean> {
    try {
      const permissions = await this.getUserPermissions(userid);
      return permissions.some(p => p.name === permissionName);
    } catch (error) {
      console.error('检查用户权限失败:', error);
      return false;
    }
  }

  /**
   * 检查用户是否有指定权限（批量）
   */
  async hasPermissions(userid: string, permissionNames: string[]): Promise<{ [key: string]: boolean }> {
    try {
      const permissions = await this.getUserPermissions(userid);
      const userPermissionNames = new Set(permissions.map(p => p.name));

      const result: { [key: string]: boolean } = {};
      permissionNames.forEach(name => {
        result[name] = userPermissionNames.has(name);
      });

      return result;
    } catch (error) {
      console.error('批量检查用户权限失败:', error);
      // 返回全部为false的结果
      const result: { [key: string]: boolean } = {};
      permissionNames.forEach(name => {
        result[name] = false;
      });
      return result;
    }
  }

  /**
   * 检查用户是否具有指定角色
   */
  async hasRole(userid: string, roleName: string): Promise<boolean> {
    try {
      const userInfo = await this.getUserPermissions(userid);
      return userInfo.roles.some(role => role.name === roleName);
    } catch (error) {
      console.error('检查用户角色失败:', error);
      return false;
    }
  }

  /**
   * 检查用户是否具有PM角色
   */
  async isPM(userid: string): Promise<boolean> {
    return this.hasRole(userid, 'PM');
  }

  /**
   * 检查用户是否具有PMO角色
   */
  async isPMO(userid: string): Promise<boolean> {
    return this.hasRole(userid, 'PMO');
  }

  /**
   * 为用户分配角色
   */
  async assignUserRoles(data: AssignUserRoleData): Promise<void> {
    try {
      const { userid, roleIds, assignedBy, expiresAt } = data;

      // 检查用户是否存在
      const user = await this.databaseService.client.user.findUnique({
        where: { userid },
      });

      if (!user) {
        throw new Error(`用户不存在: ${userid}`);
      }

      // 检查角色是否存在
      const roles = await this.databaseService.client.role.findMany({
        where: { id: { in: roleIds }, isActive: true },
      });

      if (roles.length !== roleIds.length) {
        throw new Error('部分角色不存在或已禁用');
      }

      // 使用事务处理角色分配
      await this.databaseService.client.$transaction(async (tx) => {
        // 删除现有角色
        await tx.userRole.deleteMany({
          where: { userid },
        });

        // 添加新角色
        if (roleIds.length > 0) {
          await tx.userRole.createMany({
            data: roleIds.map(roleId => ({
              userid,
              roleId,
              createdBy: assignedBy,
              expiresAt,
            })),
          });
        }
      });

      console.log(`用户角色分配成功: ${userid}, 角色数量: ${roleIds.length}`);
    } catch (error) {
      console.error('分配用户角色失败:', error);
      throw error;
    }
  }

  /**
   * 为部门分配角色
   */
  async assignDepartmentRoles(data: AssignDepartmentRoleData): Promise<void> {
    try {
      const { deptId, roleIds, assignedBy, expiresAt } = data;

      // 检查部门是否存在
      const department = await this.databaseService.client.department.findUnique({
        where: { deptId },
      });

      if (!department) {
        throw new Error(`部门不存在: ${deptId}`);
      }

      // 检查角色是否存在
      const roles = await this.databaseService.client.role.findMany({
        where: { id: { in: roleIds }, isActive: true },
      });

      if (roles.length !== roleIds.length) {
        throw new Error('部分角色不存在或已禁用');
      }

      // 使用事务处理角色分配
      await this.databaseService.client.$transaction(async (tx) => {
        // 删除现有角色
        await tx.departmentRole.deleteMany({
          where: { deptId },
        });

        // 添加新角色
        if (roleIds.length > 0) {
          await tx.departmentRole.createMany({
            data: roleIds.map(roleId => ({
              deptId,
              roleId,
              createdBy: assignedBy,
              expiresAt,
            })),
          });
        }
      });

      console.log(`部门角色分配成功: ${deptId}, 角色数量: ${roleIds.length}`);
    } catch (error) {
      console.error('分配部门角色失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户的角色列表
   */
  async getUserRoles(userid: string): Promise<{
    directRoles: Role[];
    inheritedRoles: Role[];
  }> {
    try {
      const permissionInfo = await this.getUserPermissionInfo(userid);
      return {
        directRoles: permissionInfo.directRoles,
        inheritedRoles: permissionInfo.inheritedRoles,
      };
    } catch (error) {
      console.error('获取用户角色失败:', error);
      throw error;
    }
  }

  /**
   * 获取部门的角色列表
   */
  async getDepartmentRoles(deptId: number): Promise<Role[]> {
    try {
      const departmentRoles = await this.databaseService.client.departmentRole.findMany({
        where: {
          deptId,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } },
          ],
        },
        include: {
          role: true,
        },
      });

      return departmentRoles
        .filter(dr => dr.role.isActive)
        .map(dr => dr.role);
    } catch (error) {
      console.error('获取部门角色失败:', error);
      throw error;
    }
  }

  /**
   * 移除部门的特定角色
   */
  async removeDepartmentRole(deptId: number, roleId: string): Promise<void> {
    try {
      // 检查部门是否存在
      const department = await this.databaseService.client.department.findUnique({
        where: { deptId },
      });

      if (!department) {
        throw new Error(`部门不存在: ${deptId}`);
      }

      // 检查角色是否存在
      const role = await this.databaseService.client.role.findUnique({
        where: { id: roleId },
      });

      if (!role) {
        throw new Error(`角色不存在: ${roleId}`);
      }

      // 删除部门角色关联
      const deleteResult = await this.databaseService.client.departmentRole.deleteMany({
        where: {
          deptId,
          roleId,
        },
      });

      if (deleteResult.count === 0) {
        throw new Error('部门角色关联不存在');
      }

      console.log(`部门角色移除成功: 部门${deptId}, 角色${roleId}`);
    } catch (error) {
      console.error('移除部门角色失败:', error);
      throw error;
    }
  }

  /**
   * 清空部门的所有角色
   */
  async clearDepartmentRoles(deptId: number): Promise<void> {
    try {
      // 检查部门是否存在
      const department = await this.databaseService.client.department.findUnique({
        where: { deptId },
      });

      if (!department) {
        throw new Error(`部门不存在: ${deptId}`);
      }

      // 删除部门的所有角色关联
      const deleteResult = await this.databaseService.client.departmentRole.deleteMany({
        where: { deptId },
      });

      console.log(`部门角色清空成功: 部门${deptId}, 移除${deleteResult.count}个角色`);
    } catch (error) {
      console.error('清空部门角色失败:', error);
      throw error;
    }
  }
}
