#!/usr/bin/env node

/**
 * 测试清空脚本工具
 * 
 * 此脚本用于测试清空业务数据脚本的功能，包括：
 * - 数据库连接测试
 * - 表结构验证
 * - 权限检查
 * - 数据统计
 * 
 * 使用方法：
 * node scripts/test-clear-scripts.js
 * 
 * 作者：系统管理员
 * 创建时间：2025-01-22
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// 业务数据表列表
const BUSINESS_TABLES = [
  'brand',
  'project', 
  'projectRevenue',
  'weeklyBudget',
  'supplier',
  'attachment',
  'projectChangeLog',
  'approvalInstance'
];

// 系统数据表列表
const SYSTEM_TABLES = [
  'user',
  'department',
  'role',
  'permission',
  'rolePermission',
  'userRole',
  'departmentRole'
];

// 测试数据库连接
async function testDatabaseConnection() {
  console.log('🔗 测试数据库连接...');
  
  try {
    await prisma.$connect();
    console.log('✅ 数据库连接成功');
    
    // 测试基本查询
    const result = await prisma.$queryRaw`SELECT version()`;
    console.log('✅ 数据库查询正常');
    
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败：', error.message);
    return false;
  }
}

// 验证表结构
async function validateTableStructure() {
  console.log('\n📋 验证表结构...');
  
  const allTables = [...BUSINESS_TABLES, ...SYSTEM_TABLES];
  const results = {
    existing: [],
    missing: [],
    errors: []
  };
  
  for (const table of allTables) {
    try {
      // 尝试查询表的记录数
      const count = await prisma[table].count();
      results.existing.push({ table, count });
      console.log(`✅ ${table}: ${count} 条记录`);
    } catch (error) {
      if (error.message.includes('Unknown arg')) {
        results.missing.push(table);
        console.log(`❌ ${table}: 表不存在或模型未定义`);
      } else {
        results.errors.push({ table, error: error.message });
        console.log(`⚠️  ${table}: ${error.message}`);
      }
    }
  }
  
  return results;
}

// 检查删除权限
async function checkDeletePermissions() {
  console.log('\n🔐 检查删除权限...');
  
  const testResults = {
    canDelete: [],
    cannotDelete: [],
    errors: []
  };
  
  for (const table of BUSINESS_TABLES) {
    try {
      // 尝试通过计数查询来测试表访问权限
      const count = await prisma[table].count();

      // 如果能成功计数，说明有基本访问权限
      // 对于删除权限，我们假设如果有读权限通常也有写权限
      // 在实际生产环境中，应该有专门的权限检查机制
      testResults.canDelete.push(table);
      console.log(`✅ ${table}: 有删除权限`);
    } catch (error) {
      if (error.message.includes('permission denied')) {
        testResults.cannotDelete.push(table);
        console.log(`❌ ${table}: 无删除权限`);
      } else {
        testResults.errors.push({ table, error: error.message });
        console.log(`⚠️  ${table}: ${error.message}`);
      }
    }
  }
  
  return testResults;
}

// 生成数据统计报告
async function generateDataReport() {
  console.log('\n📊 生成数据统计报告...');
  
  const report = {
    businessData: {},
    systemData: {},
    total: {
      businessRecords: 0,
      systemRecords: 0
    }
  };
  
  // 统计业务数据
  console.log('\n业务数据统计：');
  for (const table of BUSINESS_TABLES) {
    try {
      const count = await prisma[table].count();
      report.businessData[table] = count;
      report.total.businessRecords += count;
      console.log(`   ${table}: ${count} 条`);
    } catch (error) {
      report.businessData[table] = 'ERROR';
      console.log(`   ${table}: 查询失败 - ${error.message}`);
    }
  }
  
  // 统计系统数据
  console.log('\n系统数据统计：');
  for (const table of SYSTEM_TABLES) {
    try {
      const count = await prisma[table].count();
      report.systemData[table] = count;
      report.total.systemRecords += count;
      console.log(`   ${table}: ${count} 条`);
    } catch (error) {
      report.systemData[table] = 'ERROR';
      console.log(`   ${table}: 查询失败 - ${error.message}`);
    }
  }
  
  console.log(`\n总计：`);
  console.log(`   业务数据记录：${report.total.businessRecords} 条`);
  console.log(`   系统数据记录：${report.total.systemRecords} 条`);
  
  return report;
}

// 检查外键依赖关系
async function checkForeignKeyDependencies() {
  console.log('\n🔗 检查外键依赖关系...');
  
  try {
    // 查询数据库中的外键约束
    const foreignKeys = await prisma.$queryRaw`
      SELECT 
        tc.table_name as table_name,
        kcu.column_name as column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name 
      FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
      WHERE tc.constraint_type = 'FOREIGN KEY'
      ORDER BY tc.table_name, kcu.column_name;
    `;
    
    console.log('外键约束关系：');
    foreignKeys.forEach(fk => {
      console.log(`   ${fk.table_name}.${fk.column_name} -> ${fk.foreign_table_name}.${fk.foreign_column_name}`);
    });
    
    return foreignKeys;
  } catch (error) {
    console.error('❌ 查询外键约束失败：', error.message);
    return [];
  }
}

// 模拟清空操作（不实际删除）
async function simulateClearOperation() {
  console.log('\n🎭 模拟清空操作（不实际删除数据）...');
  
  const simulation = {
    wouldDelete: {},
    totalWouldDelete: 0,
    errors: []
  };
  
  // 按照脚本中的删除顺序模拟
  const deleteOrder = [
    'approvalInstance',
    'projectChangeLog', 
    'projectRevenue',
    'weeklyBudget',
    'attachment',
    'project',
    'brand',
    'supplier'
  ];
  
  for (const table of deleteOrder) {
    try {
      const count = await prisma[table].count();
      simulation.wouldDelete[table] = count;
      simulation.totalWouldDelete += count;
      console.log(`   将删除 ${table}: ${count} 条记录`);
    } catch (error) {
      simulation.errors.push({ table, error: error.message });
      console.log(`   ${table}: 模拟失败 - ${error.message}`);
    }
  }
  
  console.log(`\n模拟结果：总共将删除 ${simulation.totalWouldDelete} 条业务数据记录`);
  
  return simulation;
}

// 主测试函数
async function runTests() {
  console.log('🧪 清空脚本测试工具');
  console.log('='.repeat(60));
  
  const testResults = {
    connection: false,
    tableStructure: null,
    permissions: null,
    dataReport: null,
    foreignKeys: null,
    simulation: null
  };
  
  try {
    // 1. 测试数据库连接
    testResults.connection = await testDatabaseConnection();
    if (!testResults.connection) {
      console.log('\n❌ 数据库连接失败，无法继续测试');
      return testResults;
    }
    
    // 2. 验证表结构
    testResults.tableStructure = await validateTableStructure();
    
    // 3. 检查删除权限
    testResults.permissions = await checkDeletePermissions();
    
    // 4. 生成数据统计报告
    testResults.dataReport = await generateDataReport();
    
    // 5. 检查外键依赖关系
    testResults.foreignKeys = await checkForeignKeyDependencies();
    
    // 6. 模拟清空操作
    testResults.simulation = await simulateClearOperation();
    
    // 生成测试总结
    console.log('\n📋 测试总结');
    console.log('='.repeat(40));
    
    console.log(`✅ 数据库连接：${testResults.connection ? '正常' : '失败'}`);
    console.log(`✅ 表结构验证：${testResults.tableStructure.existing.length}/${BUSINESS_TABLES.length + SYSTEM_TABLES.length} 个表正常`);
    console.log(`✅ 删除权限：${testResults.permissions.canDelete.length}/${BUSINESS_TABLES.length} 个表有权限`);
    console.log(`✅ 业务数据：${testResults.dataReport.total.businessRecords} 条记录`);
    console.log(`✅ 系统数据：${testResults.dataReport.total.systemRecords} 条记录`);
    console.log(`✅ 外键约束：${testResults.foreignKeys.length} 个约束`);
    
    if (testResults.permissions.cannotDelete.length > 0) {
      console.log(`\n⚠️  权限警告：以下表无删除权限：`);
      testResults.permissions.cannotDelete.forEach(table => {
        console.log(`   - ${table}`);
      });
    }
    
    if (testResults.tableStructure.missing.length > 0) {
      console.log(`\n⚠️  表结构警告：以下表不存在：`);
      testResults.tableStructure.missing.forEach(table => {
        console.log(`   - ${table}`);
      });
    }
    
    console.log('\n🎯 建议：');
    if (testResults.connection && 
        testResults.permissions.cannotDelete.length === 0 && 
        testResults.tableStructure.missing.length === 0) {
      console.log('   ✅ 所有检查通过，可以安全执行清空脚本');
    } else {
      console.log('   ⚠️  发现问题，请解决后再执行清空脚本');
    }
    
  } catch (error) {
    console.error('\n💥 测试过程中发生错误：', error);
  } finally {
    await prisma.$disconnect();
  }
  
  return testResults;
}

// 运行测试 - 只有在直接调用时才运行
if (process.argv[1] && process.argv[1].includes('test-clear-scripts.js')) {
  runTests().catch(console.error);
}

export {
    checkDeletePermissions, checkForeignKeyDependencies, generateDataReport, runTests, simulateClearOperation, testDatabaseConnection,
    validateTableStructure
};

