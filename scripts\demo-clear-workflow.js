#!/usr/bin/env node

/**
 * 清空业务数据工作流演示
 * 
 * 此脚本演示了完整的业务数据清空工作流程：
 * 1. 环境检查
 * 2. 数据备份
 * 3. 数据清空
 * 4. 验证结果
 * 
 * 注意：这是演示脚本，不会实际清空数据
 * 
 * 使用方法：
 * node scripts/demo-clear-workflow.js
 * 
 * 作者：系统管理员
 * 创建时间：2025-01-22
 */

import { PrismaClient } from '@prisma/client';
import {
    checkDeletePermissions,
    generateDataReport,
    simulateClearOperation,
    testDatabaseConnection,
    validateTableStructure
} from './test-clear-scripts.js';

import {
    backupBusinessData,
    clearBusinessData
} from './clear-business-data-with-backup.js';

// 手动导入 restoreBusinessData 以避免IDE格式化问题

const prisma = new PrismaClient();

// 演示配置
const DEMO_CONFIG = {
  actuallyDelete: false,  // 设为true时会实际删除数据
  createBackup: true,     // 是否创建备份
  showDetailedLogs: true  // 是否显示详细日志
};

// 步骤1：环境检查
async function step1_EnvironmentCheck() {
  console.log('🔍 步骤1：环境检查');
  console.log('='.repeat(40));
  
  // 检查数据库连接
  const connectionOk = await testDatabaseConnection();
  if (!connectionOk) {
    throw new Error('数据库连接失败，无法继续');
  }
  
  // 验证表结构
  const tableStructure = await validateTableStructure();
  if (tableStructure.missing.length > 0) {
    console.log('⚠️  警告：发现缺失的表：', tableStructure.missing);
  }
  
  // 检查权限
  const permissions = await checkDeletePermissions();
  if (permissions.cannotDelete.length > 0) {
    console.log('⚠️  警告：以下表无删除权限：', permissions.cannotDelete);
  }
  
  console.log('✅ 环境检查完成\n');
  return {
    connectionOk,
    tableStructure,
    permissions
  };
}

// 步骤2：数据统计和备份
async function step2_DataBackup() {
  console.log('📊 步骤2：数据统计和备份');
  console.log('='.repeat(40));
  
  // 生成数据报告
  const dataReport = await generateDataReport();
  
  if (DEMO_CONFIG.createBackup && dataReport.total.businessRecords > 0) {
    console.log('\n📦 开始创建备份...');
    
    if (DEMO_CONFIG.actuallyDelete) {
      // 实际创建备份
      const backupFile = `demo-backup-${Date.now()}.json`;
      const backupPath = await backupBusinessData(backupFile);
      console.log(`✅ 备份已创建：${backupPath}`);
      return { dataReport, backupPath };
    } else {
      console.log('🎭 演示模式：跳过实际备份创建');
      return { dataReport, backupPath: 'demo-backup.json' };
    }
  } else {
    console.log('ℹ️  无业务数据需要备份');
    return { dataReport, backupPath: null };
  }
}

// 步骤3：模拟清空操作
async function step3_ClearData() {
  console.log('🗑️  步骤3：清空业务数据');
  console.log('='.repeat(40));
  
  if (DEMO_CONFIG.actuallyDelete) {
    console.log('⚠️  实际删除模式：开始清空数据...');
    await clearBusinessData();
    console.log('✅ 数据清空完成');
  } else {
    console.log('🎭 演示模式：模拟清空操作...');
    const simulation = await simulateClearOperation();
    console.log(`✅ 模拟完成，将删除 ${simulation.totalWouldDelete} 条记录`);
    return simulation;
  }
}

// 步骤4：验证结果
async function step4_VerifyResults() {
  console.log('✅ 步骤4：验证清空结果');
  console.log('='.repeat(40));
  
  if (DEMO_CONFIG.actuallyDelete) {
    // 实际验证
    const afterReport = await generateDataReport();
    console.log('清空后数据统计：');
    console.log(`   业务数据：${afterReport.total.businessRecords} 条`);
    console.log(`   系统数据：${afterReport.total.systemRecords} 条`);
    
    if (afterReport.total.businessRecords === 0) {
      console.log('✅ 业务数据已完全清空');
    } else {
      console.log('⚠️  仍有业务数据残留');
    }
    
    return afterReport;
  } else {
    console.log('🎭 演示模式：跳过实际验证');
    console.log('✅ 在实际模式下，这里会验证所有业务数据已被清空');
    console.log('✅ 系统数据（用户、权限等）保持不变');
  }
}

// 步骤5：恢复演示（可选）
async function step5_RestoreDemo(backupPath) {
  if (!DEMO_CONFIG.actuallyDelete || !backupPath) {
    console.log('\n🔄 步骤5：数据恢复演示');
    console.log('='.repeat(40));
    console.log('🎭 演示模式：跳过实际恢复');
    console.log('ℹ️  如需恢复数据，可使用以下命令：');
    console.log(`   npm run clear:restore ${backupPath || 'backup-file-name.json'}`);
    return;
  }
  
  console.log('\n🔄 步骤5：数据恢复演示');
  console.log('='.repeat(40));
  console.log('⚠️  注意：这将恢复刚才清空的数据');
  
  // 这里可以添加恢复逻辑，但为了安全起见，在演示中跳过
  console.log('🎭 为安全起见，跳过自动恢复演示');
  console.log(`ℹ️  如需恢复，请手动运行：npm run clear:restore ${backupPath}`);
}

// 主演示函数
async function runDemo() {
  console.log('🎬 业务数据清空工作流演示');
  console.log('='.repeat(60));
  console.log(`演示配置：`);
  console.log(`   实际删除数据：${DEMO_CONFIG.actuallyDelete ? '是' : '否'}`);
  console.log(`   创建备份：${DEMO_CONFIG.createBackup ? '是' : '否'}`);
  console.log(`   详细日志：${DEMO_CONFIG.showDetailedLogs ? '是' : '否'}`);
  console.log('');
  
  if (!DEMO_CONFIG.actuallyDelete) {
    console.log('🛡️  安全模式：此演示不会实际删除任何数据');
    console.log('🛡️  如需实际清空数据，请修改 DEMO_CONFIG.actuallyDelete = true');
    console.log('');
  }
  
  try {
    // 步骤1：环境检查
    const envCheck = await step1_EnvironmentCheck();
    
    // 步骤2：数据备份
    const backupResult = await step2_DataBackup();
    
    // 步骤3：清空数据
    const clearResult = await step3_ClearData();
    
    // 步骤4：验证结果
    const verifyResult = await step4_VerifyResults();
    
    // 步骤5：恢复演示
    await step5_RestoreDemo(backupResult.backupPath);
    
    // 总结
    console.log('\n🎯 演示总结');
    console.log('='.repeat(40));
    console.log('✅ 所有步骤已完成');
    console.log('');
    console.log('📋 实际使用时的建议流程：');
    console.log('1. npm run clear:test     # 测试环境');
    console.log('2. npm run clear:backup   # 备份数据');
    console.log('3. npm run clear:data     # 清空数据');
    console.log('4. 验证业务功能正常');
    console.log('5. 如有问题，使用 npm run clear:restore 恢复');
    
    console.log('\n⚠️  重要提醒：');
    console.log('- 在生产环境使用前，请先在测试环境验证');
    console.log('- 确保已通知相关业务人员');
    console.log('- 建议在业务低峰期执行');
    console.log('- 保留备份文件直到确认无需恢复');
    
  } catch (error) {
    console.error('\n💥 演示过程中发生错误：', error.message);
    console.error('详细错误信息：', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 处理命令行参数
function parseArguments() {
  const args = process.argv.slice(2);
  
  if (args.includes('--actually-delete')) {
    DEMO_CONFIG.actuallyDelete = true;
    console.log('⚠️  警告：启用实际删除模式！');
  }
  
  if (args.includes('--no-backup')) {
    DEMO_CONFIG.createBackup = false;
  }
  
  if (args.includes('--quiet')) {
    DEMO_CONFIG.showDetailedLogs = false;
  }
  
  if (args.includes('--help')) {
    console.log('业务数据清空工作流演示');
    console.log('');
    console.log('使用方法：');
    console.log('  node scripts/demo-clear-workflow.js [选项]');
    console.log('');
    console.log('选项：');
    console.log('  --actually-delete  实际删除数据（危险！）');
    console.log('  --no-backup       跳过备份创建');
    console.log('  --quiet           减少日志输出');
    console.log('  --help            显示此帮助信息');
    console.log('');
    console.log('示例：');
    console.log('  node scripts/demo-clear-workflow.js                # 安全演示模式');
    console.log('  node scripts/demo-clear-workflow.js --actually-delete  # 实际删除模式');
    process.exit(0);
  }
}

// 运行演示 - 只有在直接调用时才运行
if (process.argv[1] && process.argv[1].includes('demo-clear-workflow.js')) {
  parseArguments();
  runDemo().catch(console.error);
}

export {
    runDemo,
    step1_EnvironmentCheck,
    step2_DataBackup,
    step3_ClearData,
    step4_VerifyResults,
    step5_RestoreDemo
};

