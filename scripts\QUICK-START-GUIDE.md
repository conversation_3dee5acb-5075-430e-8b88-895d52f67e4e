# 业务数据清空工具 - 快速开始指南

## 🚀 一分钟快速上手

### 第一次使用

```bash
# 1. 查看演示（安全，不会删除任何数据）
npm run clear:demo

# 2. 测试环境和权限
npm run clear:test

# 3. 备份当前数据
npm run clear:backup

# 4. 清空业务数据（会自动备份）
npm run clear:data
```

### 如需恢复数据

```bash
# 查看备份文件
ls backups/

# 恢复数据（替换为实际的备份文件名）
npm run clear:restore business-data-backup-2025-07-22T07-18-15.json
```

## 📋 可用命令

| 命令 | 功能 | 安全性 |
|------|------|--------|
| `npm run clear:demo` | 查看完整演示 | ✅ 安全（不删除数据） |
| `npm run clear:test` | 测试环境和权限 | ✅ 安全（只读检查） |
| `npm run clear:backup` | 仅备份数据 | ✅ 安全（只备份） |
| `npm run clear:data` | 清空数据（自动备份） | ⚠️ 会删除业务数据 |
| `npm run clear:restore <file>` | 恢复数据 | ⚠️ 会覆盖现有数据 |
| `npm run clear:simple` | 简单清空（无备份） | ❌ 危险（无备份） |

## 🎯 典型使用场景

### 开发环境重置
```bash
npm run clear:test    # 检查环境
npm run clear:data    # 清空数据
```

### 测试前准备
```bash
npm run clear:backup  # 备份当前状态
npm run clear:data    # 清空数据
# 运行测试...
npm run clear:restore backup-file.json  # 恢复数据
```

### 生产环境使用（谨慎！）
```bash
npm run clear:demo    # 先看演示
npm run clear:test    # 检查环境
npm run clear:backup  # 手动备份
npm run clear:data    # 清空数据
```

## 📊 数据分类

### 会被清空的业务数据
- ✅ 品牌数据 (brands)
- ✅ 项目数据 (projects)
- ✅ 项目收入 (project_revenues)
- ✅ 周预算 (weekly_budgets)
- ✅ 供应商 (suppliers)
- ✅ 附件 (attachments)
- ✅ 项目变更日志 (project_change_logs)
- ✅ 审批实例 (approval_instances)

### 会保留的系统数据
- 🔒 用户数据 (users)
- 🔒 部门数据 (departments)
- 🔒 角色数据 (roles)
- 🔒 权限数据 (permissions)
- 🔒 角色权限关联 (role_permissions)
- 🔒 用户角色关联 (user_roles)
- 🔒 部门角色关联 (department_roles)

## ⚠️ 重要提醒

### 安全检查清单
- [ ] 已在测试环境验证脚本
- [ ] 已备份重要数据
- [ ] 已通知相关业务人员
- [ ] 已停止相关业务服务
- [ ] 确认数据库连接正确
- [ ] 确认有足够的磁盘空间存储备份

### 最佳实践
1. **先演示后实操**：使用 `npm run clear:demo` 了解流程
2. **测试优先**：使用 `npm run clear:test` 检查环境
3. **备份为王**：清空前务必备份，保留备份文件
4. **分步执行**：不要一次性执行，分步骤确认
5. **低峰操作**：在业务低峰期执行

### 故障恢复
如果出现问题：
1. 检查错误日志
2. 确认数据库连接
3. 使用备份文件恢复：`npm run clear:restore <backup-file>`
4. 联系技术支持

## 🔧 技术细节

### 环境要求
- Node.js 18+
- PostgreSQL 数据库
- 正确配置的 DATABASE_URL 环境变量
- 足够的数据库权限（SELECT, DELETE）

### 备份格式
- 格式：JSON
- 位置：`./backups/` 目录
- 命名：`business-data-backup-YYYY-MM-DDTHH-mm-ss.json`
- 内容：完整的业务数据，包含时间戳和版本信息

### 执行顺序
清空时按以下顺序删除（避免外键约束错误）：
1. approval_instances
2. project_change_logs
3. project_revenues
4. weekly_budgets
5. attachments
6. projects
7. brands
8. suppliers

## 📞 获取帮助

### 常见问题
1. **权限不足**：确保数据库用户有DELETE权限
2. **连接失败**：检查DATABASE_URL配置
3. **外键错误**：脚本已处理，如仍出现请联系支持
4. **备份失败**：检查磁盘空间和文件权限

### 联系支持
- 查看详细日志获取错误信息
- 检查 `scripts/README-clear-business-data.md` 获取完整文档
- 联系系统管理员或开发团队

---

**版本**：1.0  
**更新时间**：2025-07-22  
**状态**：已测试，可用于生产环境
